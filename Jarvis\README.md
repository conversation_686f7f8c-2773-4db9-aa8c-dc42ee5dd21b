# 📌Introduction

<img src="https://github.com/kishanrajput23/<PERSON>-Desktop-Voice-Assistant/blob/main/Images/Cover_pic.jpg" alt="">

- A virtual assistant, also called an AI assistant or digital assistant, is an application program that understands natural language voice commands and completes tasks for the user. The whole concept based on how can we make our life easier or how can we automate the things by just using our voice command. When the project is being executed then first of all it greets the user as per the time. After that it just listen your command in form of voice and just that that thing according to your command.

- It is implemented in **“PYTHON Programming Language”** in which implementation is very easy. For every sort of work there is module present in python which makes the thing very easy and effective to do. It is user friendly and easy to understandable for beginner.

# 📌Moules/Libraries Used

### 🔸Pyttsx3
- A python library that will help us to convert text to speech. In short, it is a text-to-speech library.
- It works offline, and it is compatible with Python 2 as well as Python 3.

### 🔸Datetime
- To provide current or live time to Assistant.
- Used for greeting user according to time.

### 🔸Speech Recognition
- Library for performing speech recognition, with support for several engines and APIs, online and offline.
- Used for taking input from microphone as a source to perform tasks.

### 🔸Wikipedia
- Wikipedia is a Python library that makes it easy to access and parse data from Wikipedia.
- It helps the user to get results for a particular query or search.

### 🔸Web Browser
- The web browser module provides a high-level interface to allow displaying Web-based documents to users
- Under most circumstances, simply calling the open() function from this module will do the right thing.

### 🔸OS
- The OS module in Python provides functions for interacting with the operating system.
- This module provides a portable way of using operating system-dependent functionality.

### 🔸Random
- We can generate random numbers in Python by using random module.
- These are pseudo-random number as the sequence of number generated depends on the seed.

### 🔸PyAutoGui
- Pyautogui is a library that allows you to control the mouse and keyboard to do various things.
- In this project we use this library for taking screenshots of the screen.

# 📌Features 

### 👉 1. Greet the user

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture1.png" alt="">

### 👉 2. Tell current time & date

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture2.png" alt="">

### 👉 3. Search something on Wikipedia

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture3.png" alt="">

### 👉 4. Open any Website

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture4.png" width="480"/> <img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture5.png" width="480"/>

### 👉 5. Plays Music

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture6.png" alt="">

### 👉 6. Can search anything on Google

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture7.png" width="480"/> <img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture8.png" width="480"/> 

### 👉 7. Take important note in text file

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture9.png" alt="">

### 👉 8. Take Screenshots and save it with custom filename

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture10.png" alt="">

### 👉 9. Finally make Assistant offline

<img src="https://github.com/kishanrajput23/Jarvis-Desktop-Voice-Assistant/blob/main/Images/Picture11.png" alt="">

# 📌WHY TO USE JARVIS?

1. It fulfils the own personnel desktop voice assistant.

2. It has an easy to install and use interface.

3. It accepts inputs even through voice or keyboard.

4. It automates tedious tasks like deployment, unit testing through a single command.

# 📌Advantages / Disadvantages 

|  **S.No.**  |  **Advantages**  | **Disadvantages**  |
|:-----------:|:----------------:|:------------------:|
|  **1.**  |  Secure |  Costly  |
|  **2.**  |  Easy to use |  Expensive equipments  |
|  **3.**  |  Custom commands  |  Limited language support  |
|  **4.**  |  Helpful for disabled ones  |  It cannot work in noisy environments  |
|  **5.**  |  Can't work with variety of commands  |  Can't use for more than one person at a time  |

# 📌Conclusion

- Through this voice assistant, we have automated various services using a single line command. It eases most of the tasks of the user like searching the web, retrieving weather forecast details, vocabulary help and medical related queries. 

- We aim to make this project a complete server assistant and make it smart enough to act as a replacement for a general server administration. The future plans include integrating Jarvis with mobile using React Native to provide a synchronized experience between the two connected devices. 

- Further, in the long run, Jarvis is planned to feature auto deployment supporting elastic beanstalk, backup files, and all operations which a general Server Administrator does. The functionality would be seamless enough to replace the Server Administrator with Jarvis. 
